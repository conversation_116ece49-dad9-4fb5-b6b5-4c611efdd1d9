.modal {
  :global(.ant-modal-content) {
    border-radius: 20px;
    padding: 0;
    width: 546px;
    height: 636px;
    overflow: hidden;
    background-color: var(--desktop-modal-bg-color);
  }

  :global(.ant-modal-header) {
    padding: 20px 24px 16px;
    text-align: center;
    background-color: var(--desktop-modal-bg-color);

    :global(.ant-modal-title) {
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
    }
  }

  :global(.ant-modal-close) {
    left: 15px;
  }

  :global(.ant-modal-body) {
    padding: 0;
  }

    
}

.container {
  display: flex;
  flex-direction: column;
  height: 550px;
}

.breadcrumbContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow-x: auto;
  padding: 16px 24px;
  white-space: nowrap;
  background-color: var(--desktop-modal-bg-color);

  &::-webkit-scrollbar {
    display: none;
  }
}

.breadcrumbContent {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.breadcrumbNextContainer {
  display: flex;
  align-items: center;
  margin: 0 5px;
}

.breadcrumbNextImg {
  height: 16px;
}

.breadcrumbItem {
  font-size: 14px;
  color: rgba(140, 147, 176, 1);
  cursor: pointer;
  width: 100px;
  text-align: center;
  padding: 5px 10px;
  border-radius: 20px;
  background-color: rgba(140, 147, 176, 0.1);
  transition: background-color 0.2s;
}

.breadcrumbItemActive {
  color: rgba(255, 178, 29, 1);
  width: 100px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  background-color: rgba(255, 178, 29, 0.15);
}

.fileList {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
  gap: 8px;
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: #999;
  font-size: 14px;
}

.fileItem {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-radius: 16px;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--card-active-background-color);
    border-radius: 16px;
  }

  &.selected {
    background-color: var(--card-active-background-color);
    border-radius: 16px;
  }
}

.fileIcon {
  width: 40px;
  height: 40px;
  background-color: #ffa940;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;

  :global(.anticon) {
    color: white;
    font-size: 20px;
  }
}

.fileInfo {
  flex: 1;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.fileName {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 4px;
  font-family: MiSans;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  line-height: 1.4;
  max-height: 2.8em; /* 2行的高度 */
  align-items: flex-start;
  gap: 8px;
}

.heartIcon {
  color: #ff4d4f;
  font-size: 14px;
}

.fileTime {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.itemCount {
  font-size: 12px;
  color: #999;
}

.fileDetails {
  font-size: 12px;
  color: #999;
}

.checkboxContainer {
  margin-left: 12px;
}

.customCheckbox {
  width: 20px;
  height: 20px;
  border: 2px solid #d9d9d9;
  border-radius: 4px;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;

  &.checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);

    &::after {
      content: '';
      position: absolute;
      left: 6px;
      top: 2px;
      width: 6px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }

  &:hover {
    border-color: var(--primary-color);
  }
}

.footer {
  padding: 16px 24px;
  display: flex;
  gap: 12px;

  :global(.ant-btn-variant-outlined) {
    &:disabled {
      background: var(--primary-color);
      color: var(--text-color);
      opacity: 0.5;
    }
  }

  :global(.ant-btn-variant-outlined:not(:disabled):hover) {
    // opacity: 0.7;
    background: var(--primary-color);
  }
}


.cancelButton {
  flex: 1;
  border-radius: 16px;
  width: 239px;
  height: 50px;
  font-weight: 500;
  font-size: 16px;
  background-color: var(--componentcard-btn-bg-color);
  border: none;
  color: var(--text-color);
  font-family: MiSans;

  :global(.ant-btn-variant-outlined) {
    height: 100%;
    width: 100%;
  }

  :global(.ant-btn-variant-outlined:not(:disabled):hover) {
    opacity: 0.5;
    background: var(--table-hover-bg);
    color: var(--text-color);
  }

}

.confirmButton {
  flex: 1;
  height: 50px;
  border-radius: 16px;
  font-size: 16px;
  width: 239px;
  font-weight: 500;
  font-family: MiSans;
  height: 50px;
  // color: #fff;
  background-color: #3482FF;
  display: flex;
  justify-content: center;
  align-items: center;

  :global {

    .ant-btn-variant-solid:disabled,
    .ant-btn-variant-solid {
      color: #fff;
      border: none;
      background-color: transparent;
      font-weight: 500;
      font-family: MiSans;
      font-size: 17px;

    }
  }


  //   border-color: #1890ff;
  :global(.ant-btn-variant-outlined) {
    height: 100%;
    width: 100%;
    background-color: var(--primary-color);
    border-radius: 16px;
    color: #fff;
    border: none;
  }


}