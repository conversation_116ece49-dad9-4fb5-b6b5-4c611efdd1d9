import React, { useState, useEffect, useCallback } from 'react';
import { Modal, Button, Avatar, Checkbox, Spin } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import styles from './index.module.scss';
import { PreloadImage } from '@/components/Image';
import close from '@/Resources/icon/close.png';
import close_white from '@/Resources/icon/close_white.png';
import { useTheme } from '@/utils/themeDetector';
import { useFatUser, type FatUser } from '@/hooks/useFatUser';

// 使用新的 FatUser 类型替代原有的 User 接口
type User = FatUser;

interface UserSelectorProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (selectedUsers: User[]) => void;
  selectedUserIds: string[];
  title?: string;
}
 
const UserSelector: React.FC<UserSelectorProps> = ({
  visible,
  onClose,
  onConfirm,
  selectedUserIds,
  title = '添加用户'
}) => {
  const { isDarkMode } = useTheme() as any;
  const [tempSelectedIds, setTempSelectedIds] = useState<string[]>([]);
  
  // 使用 useFatUser hook 获取用户数据
  const { users, isLoading: userListLoading, loadUsers } = useFatUser();

  // 初始化用户列表
  useEffect(() => {
    if (visible) {
      loadUsers();
    }
  }, [visible, loadUsers]);

  // 当弹窗打开时，初始化临时选中状态
  useEffect(() => {
    if (visible) {
      setTempSelectedIds([...selectedUserIds]);
    }
  }, [visible, selectedUserIds]);

  // 处理用户选择
  const handleUserSelect = useCallback((userId: string, checked: boolean) => {
    try {
      // 如果用户已经在外部选中列表中，不允许取消勾选
      if (selectedUserIds.includes(userId) && !checked) {
        return;
      }

      setTempSelectedIds(prev => {
        if (checked) {
          return [...prev, userId];
        } else {
          return prev.filter(id => id !== userId);
        }
      });
    } catch (error) {
      console.error('Error selecting user:', error);
    }
  }, [selectedUserIds]);

  // 确认选择
  const handleConfirm = useCallback(() => {
    try {
      const selectedUsers = users.filter(user => tempSelectedIds.includes(user.id));
      onConfirm(selectedUsers);
      onClose();
    } catch (error) {
      console.error('Error confirming selection:', error);
    }
  }, [tempSelectedIds, users, onConfirm, onClose]);

  // 取消选择
  const handleCancel = useCallback(() => {
    setTempSelectedIds([...selectedUserIds]);
    onClose();
  }, [selectedUserIds, onClose]);

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      closeIcon={<PreloadImage style={{width: '20px', height: '20px'}} src={isDarkMode ? close_white: close } alt="关闭" />}
      footer={null}
      width={546}
      centered
      className={styles.modal}
      okButtonProps={{
        className: styles.confirmButton
      }}
      destroyOnClose={true}
      maskClosable={false}
    >
      <div className={styles.container}>
        {/* 内容区域 - 可滚动 */}
        <div className={styles.scrollableContent}>
          {/* 加载状态 */}
          {userListLoading && (
            <div className={styles.loadingContainer}>
              <Spin size="small" />
              <span>加载中...</span>
            </div>
          )}
          
          {/* 用户列表 */}
          <div className={styles.userList}>
            {users.map(user => {
              const isSelected = tempSelectedIds.includes(user.id);
              const isAlreadySelected = selectedUserIds.includes(user.id);
              
              return (
                <div key={user.id} className={styles.userItem}>
                  <div className={styles.userInfo}>
                    <Avatar 
                      size={50}
                      src={user.avatar}
                      icon={<UserOutlined />}
                      className={styles.avatar}
                    />
                    <div className={styles.userDetails}>
                      <div className={styles.userName}>{user.name}</div>
                      <div className={styles.userPosition}>{user.position}</div>
                    </div>
                  </div>
                  <Checkbox
                    checked={isSelected}
                    disabled={isAlreadySelected && isSelected}
                    onChange={(e) => handleUserSelect(user.id, e.target.checked)}
                    className={styles.checkbox}
                  />
                </div>
              );
            })}
            
            {!userListLoading && users.length === 0 && (
              <div className={styles.emptyState}>
                暂无可选择的用户
              </div>
            )}
          </div>
          
        </div>
        
        {/* 底部确认按钮 - 固定在底部 */}
        <div className={styles.footer}>
          <Button 
            disabled={tempSelectedIds.length === 0}
            className={styles.confirmButton}
            onClick={handleConfirm}
          >
            确定 {tempSelectedIds.length > 0 && `(${tempSelectedIds.length})`}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default UserSelector; 