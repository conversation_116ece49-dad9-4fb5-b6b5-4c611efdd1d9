import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.module.scss';
import { IFilmCard } from '../../../../components/FATWall_APP/FilmCard';
import { MovieCardTV } from '../optPages/RecentlyPlay';
import { formatTimeAgo } from '../../FATWall_APP/Recently/RecentlyPlay';
import { getRecentlyWatched, RecentlyPlayedResponse, getMediaFiles, MediaFileResponse } from '@/api/fatWall';
import { useInViewport, useRequest } from 'ahooks';
import ErrorComponentTV from '../Error';
import { useLibraryListTV } from '..';
import { Toast } from '@/components/Toast/manager';
import { playVideoTV } from '@/api/fatWallJSBridge';
import placeholder‌_poster_big from '@/Resources/icon/placeholder‌_row_big.png';
import { defaultPageParamTV } from '../optPages/All';

const AllRecentlyPlay = () => {
  const [recentlyPlayFilm, setRecentlyPlayFilm] = useState<RecentlyPlayedResponse>({ files: [], count: 0 });
  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮
  const [currentPlayItem, setCurrentPlayItem] = useState<any>(null); // 当前要播放的项目

  // 加载更多的必要参数
  const [pageOpt, setPageOpt] = useState<{ offset: number, limit: number }>(defaultPageParamTV); // 分页参数
  const prevParams = useRef({ ...pageOpt });
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(true);

  const [loading, setLoading] = useState<boolean>(true);

  // 获取媒体文件列表的API调用
  const { run: runGetMediaFiles } = useRequest(
    getMediaFiles,
    {
      manual: true,
      onSuccess: (res: { code: number; data: MediaFileResponse }) => {
        if (res.code === 0 && res.data && res.data.files && currentPlayItem) {
          console.log('TV端AllRecentlyPlay获取到完整剧集列表:', res.data.files);
          handleStartPlay(res.data.files, currentPlayItem);
        }
      },
      onError: (error) => {
        console.error('TV端AllRecentlyPlay获取媒体文件列表失败:', error);
        Toast.show('获取剧集列表失败');
      },
    }
  );

  // 最近播放和添加的电影列表数据
  const watchedRun = useCallback(async (callback: (v: RecentlyPlayedResponse) => void, pageOpt) => {
    setLoading(true);
    const data = await getRecentlyWatched({ ...pageOpt, tv: 1 }, { showLoading: false }).catch(e => {
      console.log('获取最近播放失败：', e);
      setLoading(false);
      setIsError(true);
    })

    if (data && data.code === 0 && data.data) {
      // 最近播放的电影列表数据加载成功，更新状态
      callback({ files: data.data.files, count: 0 });
      setIsError(false);
      setLoading(false);
      // 判断是否还有更多数据可以加载
      if (data.data.count < pageOpt.limit) setHasMore(false);
    } else {
      setIsError(true);
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    watchedRun(v => setRecentlyPlayFilm(v), defaultPageParamTV);
  }, [watchedRun])

  const recentlyPlayFilmList: IFilmCard[] = useMemo(() => {
    return recentlyPlayFilm.files.map((media, index) => {
      const time = formatTimeAgo(media.last_seen_time || 0);
      const poster = media.poster ? media.poster.length > 0 ? media.poster.length > 1 ? media.poster[1] : media.poster[0] : placeholder‌_poster_big : placeholder‌_poster_big; // 数组0索引为封面图，1索引为海报
      return {
        ...media, poster: poster, progress: media.last_seen_percent,
        title: media.media_classes === '电视剧' ? `${media.media_name}第${media.episode}集` : media.media_name,
        time: `${time}`, media_id: media.media_id, type: 'play', duration: media.duration
      }
    })
  }, [recentlyPlayFilm.files])

  const recentlyPlayListByTime = useMemo(() => {
    let obj: { [key: string]: IFilmCard[] } = {
      sevenDays: [],
      oneMonths: [],
      threeMonths: []
    };

    recentlyPlayFilmList.forEach((it: any) => {
      const now = Math.floor(Date.now() / 1000);
      const seconds = now - it.last_seen_time;
      const minutes = seconds / 60;
      const hours = minutes / 60;
      const time = hours / 24; // 天数

      if (time <= 7) {
        obj['sevenDays'].push(it);
      } else if (time > 7 && time <= 31) {
        obj['oneMonths'].push(it);
      } else {
        obj['threeMonths'].push(it);
      }
    })
    return obj;
  }, [recentlyPlayFilmList])

  const timeLabel: { [key: string]: string } = {
    sevenDays: '近7天',
    oneMonths: '近1个月',
    threeMonths: '近3个月'
  }

  // 处理开始播放
  const handleStartPlay = useCallback((mediaFiles: any[], playItem: any) => {
    if (!mediaFiles || mediaFiles.length === 0) {
      Toast.show('暂无可播放的文件');
      return;
    }

    // 构建TV端videoList数组，参考RecentlyPlay逻辑
    const videoList = mediaFiles.map(file => ({
      path: file.path,
      media_id: playItem.media_id?.toString() || '0',
      file_id: file.file_id.toString(),
      duration: file.duration || 0, // 视频时长
      position: file.last_play_point || 0, // 断点信息
      isCompelete: file.seen, // 是否完整播放，转换为boolean
      audioIndex: file?.audio_index, // 音轨信息，默认为0
      subtitlePath: file.subtitle_path, // 字幕路径，暂时为空
      subtitleType: file.subtitle_type, // 字幕类型，0表示内嵌字幕
      subtitleIndex: file.subtitle_index, // 字幕索引，默认为0

    }));

    // 通过当前项的file_id在列表中找到索引位置
    let playIndex = 0;
    if (playItem.file_id) {
      const targetIndex = mediaFiles.findIndex(file => file.file_id === playItem.file_id);
      if (targetIndex !== -1) {
        playIndex = targetIndex;
        console.log(`TV端AllRecentlyPlay播放：找到file_id(${playItem.file_id})对应的索引位置：${targetIndex}`);
      } else {
        console.log(`TV端AllRecentlyPlay播放：未找到file_id(${playItem.file_id})对应的文件，将播放第一集`);
      }
    }

    // 调用TV端视频播放接口
    playVideoTV(videoList, playIndex, (res) => {
      if (res.code === 0) {
        Toast.show('开始播放');
      } else {
        Toast.show(`播放失败: ${res.msg}`);
      }
    }).catch((error) => {
      Toast.show(error.message || '播放失败');
    });

    // 清空当前播放项目状态
    setCurrentPlayItem(null);
  }, []);

  // 处理卡片点击播放
  const handleCardClick = useCallback((item: IFilmCard) => {
    console.log('TV端AllRecentlyPlay点击卡片播放:', item);

    if (!item.media_id) {
      Toast.show('缺少媒体ID，无法播放');
      return;
    }

    // 调用接口获取完整的剧集列表
    runGetMediaFiles({
      lib_id: 0, // 根据需求设置为0
      media_id: item.media_id
    });

    // 保存当前点击的项目信息，用于后续播放
    setCurrentPlayItem(item);
  }, [runGetMediaFiles]);

  // 重置
  const clearAndRefresh = useCallback(() => {
    setHasMore(true);
    setRecentlyPlayFilm({ files: [], count: 0 });

    // 重置分页参数，重新加载数据
    setPageOpt(defaultPageParamTV);
    if (prevParams.current) {
      const { limit, offset } = prevParams.current;
      if (limit === defaultPageParamTV.limit && offset === defaultPageParamTV.offset) {
        watchedRun((v) => setRecentlyPlayFilm(v), defaultPageParamTV);
      }
    }
    prevParams.current = defaultPageParamTV;
  }, [watchedRun])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.1,
  })

  useEffect(() => {
    if (inViewport) {
      setPageOpt((prev) => {
        prevParams.current = { ...prev, offset: prev.offset + prev.limit };
        return { ...prev, offset: prev.offset + prev.limit }
      })
    }
  }, [inViewport])

  // 处理分页参数变化时的数据加载
  useEffect(() => {
    if (pageOpt.offset > 0) {
      watchedRun((v) => setRecentlyPlayFilm(prev => ({
        ...prev,
        files: [...prev.files, ...v.files],
        count: prev.count + v.count
      })), pageOpt);
    }
  }, [pageOpt, watchedRun])

  const libs = useLibraryListTV();

  return (
    <ErrorComponentTV loading={loading} isError={isError} hasContent={recentlyPlayFilmList.length !== 0} hasLibrary={libs.libs.length !== 0} refresh={clearAndRefresh}
      text={isError ? '获取失败' : libs.libs.length === 0 ? '暂无媒体库' : '暂无内容'} style={{ backgroundColor: 'var(--text-color' }}
      subText={libs.libs.length === 0 ? '请在手机或电脑应用创建' : '请在手机或电脑应用中，向对应媒体库文件夹添加视频文件'}>
      <div className={styles.container}>
        <div className={styles.header}>
          <span>最近观看</span>
        </div>

        {
          Object.keys(recentlyPlayListByTime).map((key, ind) => (
            recentlyPlayListByTime[key].length !== 0 &&
            <div key={key} className={styles.file_container}>
              <span className={styles.time_title}>{timeLabel[key]}</span>
              <div className={styles.file_content}>
                {
                  recentlyPlayListByTime[key].map((item, index) => {
                    let length = 0;
                    const arr = Object.entries(recentlyPlayListByTime);
                    for (let i = 0; i < ind; i++) {
                      const dataArr = arr[i][1];
                      length += Math.ceil(dataArr.length / 4);
                    }
                    return <MovieCardTV type='play' key={`${item.title}_${index}`} focusableId={`tv-id-allRecentlyPlay-${item.title}-${index}`} focusableRow={Math.floor(index / 4) + length} focusableCol={index % 4} callback={() => handleCardClick(item)} cover={item.poster} playTime={item.progress ? item.progress.toString() : '0'} movieTitle={item.title} time={item.time} />
                  })
                }
              </div>
            </div>
          ))
        }

        {
          hasMore && <div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>
        }
      </div>
    </ErrorComponentTV>
  )
}

export default AllRecentlyPlay;