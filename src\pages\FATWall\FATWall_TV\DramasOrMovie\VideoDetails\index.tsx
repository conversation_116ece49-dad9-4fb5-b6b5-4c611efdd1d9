import { FC, useState, useEffect, useRef } from "react";
import styles from "./index.module.scss";
import { LeftOutlined, HeartOutlined, CheckOutlined, HeartFilled } from "@ant-design/icons";
import poster_test from "@/Resources/icon/backIcon_light.png";

import play_white from "@/Resources/icon/play_dashboard.png"
import { PreloadImage } from "@/components/Image";
import { useHistory, useLocation } from "react-router-dom";
import EpisodeList, { Episode } from "@/components/FATWall_PC/EpisodeList";
import TVFocusable from "../../TVFocus";
import { Toast } from "@/components/Toast/manager";

interface VideoDetailsProps {
  videoId?: string;
}

// 默认数据，确保即使API未返回数据也有值可用
const defaultVideoData = {
  title: "纸牌屋",
  rating: 8.7,
  year: 2022,
  category: "美食",
  region: "日本",
  duration: "24分钟/集",
  tags: ["4K", "HDR10+", "Dolby Atmos"],
  description: "《孤独的美食家》是一部日本美食纪录片风格的电视剧，深受广大观众的喜爱。松重丰饰演的主角\"井之头五郎\"，是个深居简出的小贩,广大观众的喜爱。松重丰饰演的主角\"井之头五郎\"，是个深居简出的小贩,广大观众的喜爱。松重丰饰演的主角\"井之头五郎\"，是个深居简出的小贩.",
  filePath: "内置磁盘/powervnging/系统共享资源中心/孤独大食家/www.dysfcom下载独享资源2005.4k.mp4",
  fileSize: "1.6GB",
  cast: [
    { id: "1", name: "松重丰", role: "井之头五郎", avatar: '' },
    { id: "2", name: "松重丰", role: "井之头五郎", avatar: '' },
    { id: "3", name: "内田朝阳", role: "旁白", avatar:  '' },
    { id: "4", name: "野村祐人", role: "导演", avatar:  '' },
    { id: "5", name: "村田雄浩", role: "客串", avatar:  '' },
    { id: "6", name: "内田朝阳", role: "旁白", avatar:  '' },
    { id: "7", name: "野村祐人", role: "导演", avatar:  '' },
    { id: "8", name: "村田雄浩", role: "客串", avatar:  '' }
  ]
};

// 默认剧集数据
const defaultEpisodes: Episode[] = [
  { id: "1", title: "第1集", thumbnail: poster_test, episodeNumber: 1, watched: true, progress: 100, favorite: true },
  { id: "2", title: "第2集", thumbnail: poster_test, episodeNumber: 2, watched: false, progress: 45, favorite: false },
  { id: "3", title: "第3集", thumbnail: poster_test, episodeNumber: 3, watched: false, progress: 0, favorite: false },
  { id: "4", title: "第4集", thumbnail: poster_test, episodeNumber: 4, watched: false, progress: 0, favorite: false },
  { id: "5", title: "第5集", thumbnail: poster_test, episodeNumber: 5, watched: false, progress: 0, favorite: false },
  { id: "6", title: "第6集", thumbnail: poster_test, episodeNumber: 6, watched: false, progress: 0, favorite: false },
  { id: "7", title: "第7集", thumbnail: poster_test, episodeNumber: 7, watched: false, progress: 0, favorite: false },
  { id: "8", title: "第8集", thumbnail: poster_test, episodeNumber: 8, watched: false, progress: 0, favorite: false },
  { id: "9", title: "第9集", thumbnail: poster_test, episodeNumber: 9, watched: false, progress: 0, favorite: false },
  { id: "10", title: "第10集", thumbnail: poster_test, episodeNumber: 10, watched: false, progress: 0, favorite: false },
];


const VideoDetails: FC<VideoDetailsProps> = ({ videoId }) => {
  // console.log('[VideoDetails] Props:', { videoId });
  const history = useHistory();
  const location = useLocation();
  const { isDrama } = location.state as { isDrama: boolean } || { isDrama: false };
  // const isTv = localStorage.getItem("deviceType") === "2"
  // 使用默认数据初始化，防止undefined错误
  const [videoData, setVideoData] = useState(defaultVideoData);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [isWatched, setIsWatched] = useState(false);
  const [showFullDescription] = useState(false);
  // const [morePopoverVisible, setMorePopoverVisible] = useState(false);
  const [episodes, setEpisodes] = useState<Episode[]>(defaultEpisodes);
  const [currentEpisodeId, setCurrentEpisodeId] = useState<string>("1");
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 处理焦点元素的滚动
  const handleFocusScroll = (item: any) => {
    if (scrollContainerRef.current && item.ref.current) {
      const container = scrollContainerRef.current;
      const element = item.ref.current;
      const elementRect = element.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      
      // 检查元素是否在可视区域外
      if (elementRect.bottom > containerRect.bottom || elementRect.top < containerRect.top) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  const getSafeContent = () => {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>加载中...</div>

        {/* 返回按钮 - 固定在顶部 */}
        <TVFocusable
          id="tv-focus-videoDetails-back-button"
          row={0}
          col={0}
          onClick={handleBack}
          className={styles.backButton}
          currentItemCallback={handleFocusScroll}
        >
          <LeftOutlined />
        </TVFocusable>
      </div>
    );
  };
  // 模拟API请求
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setHasError(false);
      try {
        // 模拟API延迟
        setTimeout(() => {
          console.log('[VideoDetails] 数据加载完成');
          setVideoData(defaultVideoData);
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error("[VideoDetails] 获取视频详情失败:", error);
        // 出错时仍使用默认数据
        setVideoData(defaultVideoData);
        setIsLoading(false);
        setHasError(true);
      }
    };

    fetchData();
  }, [videoId]);

  const handleBack = () => {
    history.goBack();
  };

  const handlePlay = () => {
    console.log("开始播放");
    // 实现播放逻辑
  };



  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  const handleToggleWatched = () => {
    const newIsWatched = !isWatched;
    setIsWatched(newIsWatched);
    Toast.show(newIsWatched ? "已标记为已观看" : "已标记为未观看");
  };



  // const move2Trashbin = useCallback((m) => {
  //   m.destroy();
  // }, [])

  // const delFile = useCallback((modal) => {
  //   modalShow(`是否确定删除文件？`, <>删除的文件将移至"回收站"，保留30天</>, (m => {
  //     m.destroy();
  //     modal.destroy();
  //   }), () => null, false, { position: 'center', okBtnText: '删除', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' } })
  // }, [])

  // // 删除
  // const del = useCallback(() => {
  //   // setMorePopoverVisible(false);

  //   const m = modalShow('确认删除吗？', (
  //     <>
  //       <div className={styles.modal_button} onClick={() => move2Trashbin(m)}>仅从媒体库移除</div>
  //       <div className={styles.modal_button} style={{ color: 'var(--emergency-text-color)' }} onClick={() => delFile(m)}>删除文件</div>
  //     </>
  //   ), () => null, () => null, false, { okBtnStyle: { display: 'none' }, cancelBtnStyle: { width: px2rem('300px'), margin: 0 }, position: 'center' })
  //   console.log('删除成功!')
  // }, [delFile, move2Trashbin,])


  const handleToggleEpisodeFavorite = (episode: Episode) => {
    setEpisodes(prevEpisodes =>
      prevEpisodes.map(ep =>
        ep.id === episode.id
          ? { ...ep, favorite: !ep.favorite }
          : ep
      )
    );
    Toast.show(episode.favorite ? "已取消收藏" : "已添加到收藏");
  };

  const handleToggleEpisodeWatched = (episode: Episode) => {
    setEpisodes(prevEpisodes =>
      prevEpisodes.map(ep =>
        ep.id === episode.id
          ? { ...ep, watched: !ep.watched }
          : ep
      )
    );
    Toast.show(episode.watched ? "已标记为未观看" : "已标记为已观看");
  };

  const handleEpisodeSelect = (episode: Episode) => {
    setCurrentEpisodeId(episode.id);
    console.log(`选择了第${episode.episodeNumber}集`);
    // 在实际应用中，可能需要加载该剧集的详细信息或开始播放
  };

  // 添加加载状态
  if (isLoading) {
    return getSafeContent();
  }

  // 添加错误处理
  if (hasError || !videoData) {
    return getSafeContent();
  }

  // 确保videoData一定存在
  const data = videoData || defaultVideoData;

  return (
    <div className={styles.container}>
      {/* 背景视频/图片 */}
      <div className={styles.videoBackground}>
        {/* 可以使用视频或图片作为背景 */}
        <img src={poster_test} alt={data.title || "视频封面"} />
      </div>

    
      {/* 可滚动的内容区域 */}
      <div className={`${styles.scrollableContent} ${isDrama ? styles.dramaScrollableContent : ''}`} ref={scrollContainerRef}>
        {/* 视频标题 */}
        <h1 className={styles.title}>{data.title || "未知标题"}</h1>

        {/* 视频信息行 */}
        <div className={styles.infoRow}>
          <span className={styles.rating}>{data.rating || "-"}/</span>
          <span>{data.year || "-"}/</span>
          <span>{data.category || "-"}</span>
          {/* <span>{data.region || "-"}</span> */}
          {/* <span>{data.duration || "-"}</span> */}
        </div>

        {/* 视频描述 */}
        <div className={styles.descriptionContainer}>
          <div className={`${styles.description} ${showFullDescription ? styles.expanded : ''}`}>
            {data.description || "暂无描述"}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className={styles.buttons}>
          <TVFocusable
            id="tv-focus-videoDetails-play-button"
            row={0}
            col={0}
            onClick={handlePlay}
            className={styles.primaryButton}
            currentItemCallback={handleFocusScroll}
          >
            <PreloadImage src={play_white} style={{ width: "40px", height: "40px" }} alt="play_white" /> 播放 23:45
          </TVFocusable>
          
          <TVFocusable
            id="tv-focus-videoDetails-favorite-button"
            row={0}
            col={1}
            onClick={handleToggleFavorite}
            className={styles.secondaryButton}
            currentItemCallback={handleFocusScroll}
          >
            {isFavorite ?
              <HeartFilled style={{ fontSize: "40px", color: "#FF4D4F" }} /> :
              <HeartOutlined style={{ fontSize: "40px", color: "white" }} />
            }
          </TVFocusable>
          
          <TVFocusable
            id="tv-focus-videoDetails-watched-button"
            row={0}
            col={2}
            onClick={handleToggleWatched}
            className={styles.secondaryButton}
            currentItemCallback={handleFocusScroll}
          >
            <CheckOutlined style={{ fontSize: "40px", color: isWatched ? "#1890FF" : "white" }} />
          </TVFocusable>
        </div>

        {/* 剧集列表 */}
        {isDrama && (
          <EpisodeList
            episodes={episodes}
            isTv={true}
            currentEpisodeId={currentEpisodeId}
            onEpisodeSelect={handleEpisodeSelect}
            onToggleFavorite={handleToggleEpisodeFavorite}
            onToggleWatched={handleToggleEpisodeWatched}
          />
        )}

        {/* 需要向上滚动才能看到的内容 */}
        <div className={isDrama ? styles.extraInfoSection : ''}>
          {/* 演员列表 */}
          <div className={styles.castSection}>
            <h3 className={styles.sectionTitle}>演员人员</h3>
            <div className={styles.castList}>
              {(data.cast || []).map((member, index) => (
                <TVFocusable
                  key={member.id}
                  id={`tv-focus-videoDetails-cast-${member.id}`}
                  row={2}
                  col={index}
                  onClick={() => console.log(`查看演员: ${member.name}`)}
                  className={styles.castItem}
                  currentItemCallback={handleFocusScroll}
                >
                  <img
                    src={member.avatar}
                    alt={member.name}
                    className={styles.castAvatar}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/images/default-avatar.jpg';
                    }}
                  />
                  <div className={styles.castName}>{member.name}</div>
                  <div className={styles.castRole}>{member.role}</div>
                </TVFocusable>
              ))}
            </div>
          </div>

          {/* 文件信息 */}
          <div>
            <h3 className={styles.sectionTitle}>文件路径</h3>
            <div className={styles.fileInfo}>
              <div className={styles.fileInfoItem}>文件路径：{data.filePath || "-"}</div>
              <div className={styles.fileInfoItem}>文件大小：{data.fileSize || "-"}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoDetails;