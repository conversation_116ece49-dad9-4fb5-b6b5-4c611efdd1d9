import { useState, useEffect, useRef } from "react";
import { List, Toast } from "antd-mobile";
import {
  useHistory,
  useRouteMatch,
  Route,
  useLocation,
} from "react-router-dom";
import { StorageContext } from "./Context/storageContext";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/icon/backIcon_light.png";
import arrowLeftDark from "@/Resources/icon/backIcon_dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { CameraInfo, listRecordCamera } from "@/api/ipc";
import { useRequest } from "ahooks";
import { PreloadImage } from "@/components/Image";

const StorageManagement = (props: { children?: React.ReactNode }) => {
  const history = useHistory();
  const location = useLocation<{
    cameraList?: CameraInfo[];
    isDataComplete?: boolean;
    ensureCompleteData?: () => void;
  }>();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  const [cameras, setCameras] = useState<CameraInfo[]>([]);
  const isInitializedRef = useRef(false);

  // 处理摄像机图标路径的函数
  const processIconPath = (icon: string): string => {
    if (!icon) return '';

    const origin = window.location.origin;
    const pathname = window.location.pathname;
    const needPath = pathname.split('/').slice(1, 4).join('/'); // APP端路径处理

    return `${origin}/${needPath}/${icon}`;
  };

  const { run: refreshCameraList } = useRequest(() => listRecordCamera({ did: [] }, { showLoading: false }), {
    manual: true,
    onSuccess: (res) => {
      if(res && (res.code !== 0)){
        Toast.show(res?.result);
        return;
      }
      const cameraList = res.data?.camera || [];
      setCameras(cameraList);
    },
    onError: (err) => {
      console.log('err: ', err);
    },
  });

  // 初始化数据和处理返回时的刷新
  useEffect(() => {
    // 当路径变为存储管理主页面时
    if (location.pathname === path) {
      // 优先使用从Home页面传递过来的数据（仅在首次进入时）
      const passedCameraList = location.state?.cameraList;
      const isDataComplete = location.state?.isDataComplete;
      const ensureCompleteData = location.state?.ensureCompleteData;

      if (passedCameraList && passedCameraList.length > 0 && !isInitializedRef.current) {
        // 首次进入且有传递数据时使用传递的数据
        setCameras(passedCameraList);
        isInitializedRef.current = true;

        // 检查数据完整性，如果数据不完整则静默获取完整数据
        if (isDataComplete === false && ensureCompleteData) {
          ensureCompleteData();
        }
      } else {
        // 从子页面返回或没有传递数据时调用接口刷新
        refreshCameraList();
      }
    }
  }, [location.pathname, path, location.state?.cameraList, location.state?.isDataComplete, location.state?.ensureCompleteData, refreshCameraList]);

  // 监听父组件的数据更新事件
  useEffect(() => {
    const handleCameraDataUpdated = (event: CustomEvent) => {
      const { cameras, isComplete } = event.detail;
      if (isComplete && cameras) {
        setCameras(cameras);
      }
    };

    window.addEventListener('cameraDataUpdated', handleCameraDataUpdated as EventListener);

    return () => {
      window.removeEventListener('cameraDataUpdated', handleCameraDataUpdated as EventListener);
    };
  }, []);

  // 初始化阈值和检测事件状态
  const [threshold, setThreshold] = useState(90);
  const [detectEvents, setDetectEvents] = useState({
    motionDetect: true,
    humanDetect: true,
    fireDetect: true,
    petDetect: true,
    // noiseDetect: true,
  });

  return (
    <StorageContext.Provider
      value={{
        threshold,
        setThreshold,
        detectEvents,
        setDetectEvents,
      }}
    >
      <div className={styles.container}>
        {props.children}
        <Route exact path={path}>
          <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
          <div className={styles.title}>存储管理</div>
          <List className={styles.cameraList}>
            {cameras.map((camera) => {
              return(
              <List.Item
                key={camera.did}
                prefix={
                  <div className={styles.cameraImg}>
                    <PreloadImage
                      src={processIconPath(camera.icon || '')}
                      style={{
                        width: 48,
                        height: 48,
                        objectFit: 'contain'
                      }}
                    />
                  </div>
                }
                title={camera.name}
                arrowIcon
                onClick={() => {
                  history.push({
                    pathname: `${path}/detail/${camera.did}`,
                    state: {
                      camera,
                      isDataComplete: location.state?.isDataComplete,
                      ensureCompleteData: location.state?.ensureCompleteData
                    }
                  });
                }}
              />
              )
          })}
          </List>
        </Route>
      </div>
    </StorageContext.Provider>
  );
};

export default StorageManagement;
