import { Route, useLocation } from "react-router-dom";
import { useRequest } from "ahooks";
import IPCHome from "./Home";
import NotAdded from "./NotAdded";
import styles from "./index.module.scss";
import { useState, useEffect, useRef, useCallback, createContext, useContext } from "react";
import { listRecordCamera, CameraInfo } from "@/api/ipc";
import { Toast } from "antd-mobile";
import XMLoading from "@/components/XMLoading";
import { KeepAlive } from 'react-activation';

const CameraAIContext = createContext<{ isHide: boolean }>({ isHide: true });
export const InfoCameraAI = () => useContext(CameraAIContext);

export default function CameraManagement(props: {
  children?: React.ReactNode;
}) {
  const [hasCameras, setHasCameras] = useState(false);
  const [cameraList, setCameraList] = useState<CameraInfo[]>([]);
  const [isDataComplete, setIsDataComplete] = useState(false); // 数据是否完整（包含米家信息）
  const location = useLocation<{ shouldRefresh?: boolean }>();
  const isInitialLoad = useRef(true);

  const [ai_is_hide, setAi_is_hide] = useState<boolean>(true); // 上下文控制AI模块是否隐藏

  // 快速加载摄像机列表（不包含米家信息）
  const { loading } = useRequest(
    () => listRecordCamera({ did: [], fast_return: true }, { showLoading: false }),
    {
      manual: false,
      onSuccess: (res) => {
        if (res && (res.code !== 0)) {
          Toast.show(res?.result);
          return;
        }
        if (res.data.hide_ai !== undefined && typeof res.data.hide_ai === 'boolean') setAi_is_hide(res.data.hide_ai); // 设置AI模块隐藏状态 默认关闭ai
        const cameras = res.data?.camera || [];
        setCameraList(cameras);
        setHasCameras(cameras.length > 0);
        setIsDataComplete(false); // 快速返回的数据不完整

        // 如果是初始加载且有摄像机，静默获取完整信息
        if (isInitialLoad.current && cameras.length > 0) {
          fetchCameraListComplete();
        }
      },
      onError: (err) => {
        console.error("摄像头列表获取失败:", err);
        setCameraList([]);
        setHasCameras(false);
      },
    }
  );

  // 获取完整摄像机信息（包含米家信息）
  const { run: fetchCameraListComplete } = useRequest(
    () => listRecordCamera({ did: [] }, { showLoading: false }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res && (res.code !== 0)) {
          // 静默失败，不显示错误信息
          return;
        }
        if (res.data.hide_ai !== undefined && typeof res.data.hide_ai === 'boolean') setAi_is_hide(res.data.hide_ai);  // 完整获取数据时再次设置
        const cameras = res.data?.camera || [];
        setCameraList(cameras);
        setHasCameras(cameras.length > 0);
        isInitialLoad.current = false;
        setIsDataComplete(true); // 完整数据已加载

        // 通知子页面数据已更新（通过自定义事件）
        window.dispatchEvent(new CustomEvent('cameraDataUpdated', {
          detail: { cameras, isComplete: true }
        }));
      },
      onError: (err) => {
        Toast.show({
          content: "获取摄像机数据失败",
          position: "bottom",
        })
      },
    }
  );

  // 提供刷新摄像头列表的方法（获取完整信息）
  const refreshCameraList = useCallback(() => {
    isInitialLoad.current = false;
    fetchCameraListComplete();
  }, [fetchCameraListComplete]);

  // 提供获取完整数据的方法，供子组件调用
  const ensureCompleteData = useCallback(() => {
    if (!isDataComplete) {
      fetchCameraListComplete();
    }
  }, [isDataComplete, fetchCameraListComplete]);

  // 监听路由状态变化，如果需要刷新则重新获取数据
  useEffect(() => {
    if (location.state?.shouldRefresh) {
      refreshCameraList();
    }
  }, [location.state?.shouldRefresh, refreshCameraList]);

  if (loading)
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingContent}>
          <XMLoading loading={true} size={22} />
          <span className={styles.loadingText}>加载中</span>
        </div>
      </div>
    );

  return (
    <div id="cameraManagementContainer" className={styles.cameraManagementContainer}>
      <div className={styles.top}></div>
      <div className={styles.content}>
        <CameraAIContext.Provider value={{ isHide: ai_is_hide }}>
          {props.children}
        </CameraAIContext.Provider>
        <Route exact path="/cameraManagement_app">
          {hasCameras ? (
            <KeepAlive name="IPC_HOME" when={true}>
              <CameraAIContext.Provider value={{ isHide: ai_is_hide }}>
                <IPCHome
                  cameraList={cameraList}
                  refreshCameraList={refreshCameraList}
                  isDataComplete={isDataComplete}
                  ensureCompleteData={ensureCompleteData}
                  loading={loading}
                />
              </CameraAIContext.Provider>
            </KeepAlive>
          ) : (
            <NotAdded />
          )}
        </Route>
      </div>
    </div>
  );
}
