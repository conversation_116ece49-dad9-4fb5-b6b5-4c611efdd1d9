import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRequest } from "ahooks";
import { But<PERSON>, Checkbox } from "antd";
import { CaretUpOutlined, CaretDownOutlined } from "@ant-design/icons";
import styles from "./index.module.scss";
import { useTheme } from "@/utils/themeDetector";
import outline from "@/Resources/icon/outline.png";
import outlineDark from "@/Resources/icon/outline-dark.png";
import {
  getBaiduNetdiskFileList,
  downloadFromBaiduNetdisk,
  BaiduDownloadPathItem,
} from "@/api/nasDisk";
import { getPoolInfo } from "@/api/nasDisk";
import { PreloadImage } from "@/components/Image";
import folderIcon from "@/Resources/icon/file-icon.png";
import { useTaskNotification } from "@/layouts/Layout";
import fileIcon from "@/Resources/icon/file-icon.png";
import imageIcon from "@/Resources/icon/imgIcon.png";
import docIcon from "@/Resources/icon/textIcon.png";
import btIcon from "@/Resources/icon/btIcon.png";
import audioIcon from "@/Resources/icon/musicIcon.png";
import appIcon from "@/Resources/icon/otherIcon.png";
import next from "@/Resources/icon/next.png";
import next_dark from "@/Resources/icon/next_dark.png";
import EmptyState from "../components/EmptyState";
import pdfIcon from "@/Resources/icon/pdfIcon.png";
import zipIcon from "@/Resources/icon/zipIcon.png";
import pptIcon from "@/Resources/icon/pptIcon.png";
import wordIcon from "@/Resources/icon/wordIcon.png";
import xlsIcon from "@/Resources/icon/xlsIcon.png";
import textIcon from "@/Resources/icon/textIcon.png";
import { getFileType, FileTypes } from "@/utils/fileTypeUtils";
import UploadModal from "../components/UploadModal";
import DownloadModal from "../components/DownloadModal";
import { useUser } from "@/utils/UserContext";
import { Toast } from "@/components/Toast/manager";

// 定义文件项类型
interface FileItem {
  id: number;
  name: string;
  type: "file" | "folder";
  time: string;
  icon: string;
  size?: number;
  children?: FileItem[];
  category?: number; // 文件类型：1 视频、2 音频、3 图片、4 文档、5 应用、6 其他、7 种子
  thumbs?: {
    icon?: string;
    url1?: string;
    url2?: string;
    url3?: string;
    [key: string]: string | undefined;
  }; // 图片缩略图URL对象
}

const BDfiles: React.FC = () => {
  const { isDarkMode } = useTheme();
  const { userInfo } = useUser();
  const { nas_vip } = userInfo || {};
  const { addDownloadNotification, addUploadNotification } = useTaskNotification();

  // 文件列表数据
  const [fileData, setFileData] = useState<FileItem[]>([]);

  // 当前路径
  const [currentPath, setCurrentPath] = useState("/");

  // 面包屑导航状态
  const [breadcrumbs, setBreadcrumbs] = useState<
    { label: string; path: string }[]
  >([{ label: "百度网盘", path: "/" }]);

  // 排序方式
  const [sortType, setSortType] = useState(0); // 0: 名称, 1: 大小, 2: 时间
  const [sortOrder, setSortOrder] = useState(0); // 0: 升序, 1: 降序

  // 添加选择功能
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [hoveredItem, setHoveredItem] = useState<number | null>(null);
  // 失败重试次数
  const retryCountRef = useRef<number>(0);
  const MAX_RETRY_COUNT = 5;

  // 上传文件Modal状态
  const [uploadModalVisible, setUploadModalVisible] = useState(false);

  // 下载文件Modal状态
  const [downloadModalVisible, setDownloadModalVisible] = useState(false);

  // 存储池和WebDAV配置信息
  const [defaultDownloadPath, setDefaultDownloadPath] = useState("");
  const [, setWebDAVConfig] = useState<{ alias_root?: string } | null>(null);

  // 获取存储池信息
  const { run: fetchPoolInfo } = useRequest(getPoolInfo, {
    manual: true,
    onSuccess: (response: any) => {
      if (response.code === 0 && response.data) {
        // 保存WebDAV配置
        if (response.data.webDAV) {
          setWebDAVConfig(response.data.webDAV);
        }

        // 获取第一个存储池的顶层目录作为默认下载路径
        if (response.data.internal_pool.length > 0) {
          const firstPool = response.data.internal_pool[0];

          if (response.data.webDAV?.alias_root) {
            const dataDir = firstPool.data_dir.endsWith("/")
              ? firstPool.data_dir.slice(0, -1)
              : firstPool.data_dir;
            const aliasRoot = response.data.webDAV.alias_root;

            // 设置默认的下载路径
            const defaultPath = `${aliasRoot}${dataDir}/百度网盘`;
            setDefaultDownloadPath(defaultPath);
          }
        }
      }
    },
    onError: (error) => {
      console.error("获取存储池信息失败：", error);
    },
  });

  // 组件挂载时获取存储池信息
  useEffect(() => {
    fetchPoolInfo();
  }, [fetchPoolInfo]);

  // 下载请求
  const { run: runDownload } = useRequest(
    (params: {
      remotePath: BaiduDownloadPathItem[];
      localPath: string;
      autotask: number;
    }) => {
      return downloadFromBaiduNetdisk({
        action: "download",
        autotask: params.autotask,
        remotepath: params.remotePath,
        localpath: params.localPath,
      });
    },
    {
      manual: true,
      onSuccess: (result: any) => {
        if (result.code === 0) {
          Toast.show("下载任务添加成功");
          // 下载后清空选中状态
          setSelectedItems([]);
          // 添加下载任务通知
          addDownloadNotification();
        } else {
          Toast.show(`下载任务添加失败: ${result.result}`);

          if (result.failed_paths && result.failed_paths.length > 0) {
            console.error("下载失败的路径:", result.failed_paths);
          }
        }
      },
      onError: (error) => {
        Toast.show("下载请求出错，请重试");
        console.error("下载请求出错:", error);
      },
    }
  );

  // 使用 useRequest 获取文件列表
  const { run: fetchFileList, loading: fetchFileListLoading } = useRequest(
    (params: { path: string; order: string; desc: number }) => {
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: encodeURIComponent(params.path),
        order: params.order,
        desc: params.desc,
        web: 1, // 添加web参数，确保返回缩略图数据
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if(response?.code === 4){
            if (retryCountRef.current < MAX_RETRY_COUNT) {
              retryCountRef.current += 1;
              loadFileList();
            } else {
              console.error("重试次数已达上限，停止重试");
              retryCountRef.current = 0; 
            }
            return;
        }
        retryCountRef.current = 0;
        if (response && response.errno === 0) {
          // 将百度网盘文件列表转换为应用内文件列表格式
          const files: FileItem[] = response.list.map((item: any) => ({
            id: item.fs_id,
            name: item.server_filename,
            type: item.isdir === 1 ? "folder" : "file",
            time: new Date(item.server_mtime * 1000).toLocaleString(),
            icon:
              item.thumbs && typeof item.thumbs === "object"
                ? item.thumbs.icon || item.thumbs.url1 || ""
                : "https://example.com/file-icon.png",
            size: item.size || 0,
            children: item.dir_empty === 0 ? [] : undefined, // 如果目录不为空，预设一个空数组
            category: item.category, // 添加分类属性
            thumbs: item.thumbs, // 直接传递thumbs对象
          }));

          setFileData(files);
        } else {
          console.error("获取文件列表失败:", response);
        }
      },
      onError: (error) => {
        console.error("加载文件列表出错:", error);
      },
    }
  );

  // 加载文件列表
  const loadFileList = useCallback(() => {
    // 获取对应的排序字段
    const orderField =
      sortType === 0 ? "name" : sortType === 1 ? "size" : "time";

    // 使用 useRequest 发起请求
    fetchFileList({
      path: currentPath,
      order: orderField,
      desc: sortOrder,
    });
  }, [currentPath, sortType, sortOrder, fetchFileList]);

  // 当排序条件或当前路径变化时重新加载文件列表
  useEffect(() => {
    loadFileList();
  }, [loadFileList]);

  // 获取文件图标
  const getFileIcon = (item: FileItem): string => {
    if (item.type === "folder") {
      return folderIcon;
    }

    // 如果是图片且有缩略图，使用缩略图
    if ((item.category === 3 || item.category === 1) && item.thumbs) {
      return (
        item.thumbs.icon ||
        item.thumbs.url1 ||
        item.thumbs.url2 ||
        item.thumbs.url3 ||
        imageIcon
      );
    }
    const fileType = getFileType(item.name, false);
    // 根据文件类型返回对应图标
    switch (fileType) {
      case FileTypes.PDF:
        return pdfIcon;
      case FileTypes.ZIP:
        return zipIcon;
      case FileTypes.WORD:
        return wordIcon;
      case FileTypes.EXCEL:
        return xlsIcon;
      case FileTypes.PPT:
        return pptIcon;
      case FileTypes.TEXT:
        return textIcon;
      case FileTypes.AUDIO:
        return audioIcon;
      default:
        // 对于其他类型，保持原有的category逻辑作为后备
        switch (item.category) {
          case 2:
            return audioIcon; // 音频图标
          case 4:
            return docIcon; // 文档图标
          case 5:
            return appIcon; // 应用图标
          case 7:
            return btIcon; // 种子文件使用默认图标
          default:
            return fileIcon; // 默认图标
        }
    }
  };

  // 处理文件夹导航
  const navigateToFolder = (path: string, folderName?: string) => {
    setCurrentPath(path);

    // 更新面包屑
    if (folderName) {
      // 如果直接点击文件夹，添加新的面包屑项
      setBreadcrumbs((prev) => [...prev, { label: folderName, path }]);
    }
  };

  // 处理面包屑点击
  const handleBreadcrumbClick = (
    item: { label: string; path: string },
    index: number
  ) => {
    // 设置当前路径
    setCurrentPath(item.path);

    // 更新面包屑，截取到点击的位置
    setBreadcrumbs((prev) => prev.slice(0, index + 1));
  };

  // 处理文件夹点击
  const handleFolderClick = (folder: FileItem) => {
    // 构建新路径，确保格式正确
    const newPath =
      currentPath === "/" ? `/${folder.name}` : `${currentPath}/${folder.name}`;

    // 更新路径并加载文件列表
    navigateToFolder(newPath, folder.name);
  };

  // 处理文件点击
  const handleFileClick = (file: FileItem) => {
    console.log("点击文件:", file);
    // 实现文件预览或下载逻辑
  };

  // 处理点击事件
  const handleItemClick = (item: FileItem) => {
    if (item.type === "folder") {
      handleFolderClick(item);
    } else {
      handleFileClick(item);
    }
  };

  // 处理选择变化
  const handleSelectionChange = (id: number, checked: boolean) => {
    if (checked) {
      setSelectedItems((prev) => [...prev, id]);
    } else {
      setSelectedItems((prev) => prev.filter((itemId) => itemId !== id));
    }
  };



  // 处理下载选中项
  const handleDownloadSelected = () => {
    if (selectedItems.length === 0) {
      return;
    }

    if (nas_vip !== 1) {
      // 非VIP用户直接调用下载，使用默认路径
      const selectedFiles = fileData.filter((file) =>
        selectedItems.includes(file.id)
      );

      // 准备下载参数
      const remotePath: BaiduDownloadPathItem[] = selectedFiles.map((item) => ({
        type: item.type === "folder" ? "directory" : "file",
        path:
          currentPath === "/" ? `/${item.name}` : `${currentPath}/${item.name}`,
      }));

      // 使用默认下载路径
      runDownload({
        remotePath,
        localPath: defaultDownloadPath,
        autotask: 0,
      });

      return;
    }

    // VIP用户打开下载位置选择Modal
    setDownloadModalVisible(true);
  };

  // 处理上传文件
  const handleUpload = () => {
    // PC端网盘文件页面上传按钮埋点
    window.onetrack?.('track', 'nasDisk_baiduNetdisk_add_click');

    setUploadModalVisible(true);
  };

  // 处理文件上传完成
  const handleUploadComplete = (selectedPaths: string[]) => {
    loadFileList();
    // 添加上传任务通知
    addUploadNotification();
  };

  // 处理文件下载完成
  const handleDownloadComplete = (
    selectedPath: string,
    displayPath: string
  ) => {
    console.log("下载到路径:", selectedPath);
    console.log("显示路径:", displayPath);
    const selectedFiles = fileData.filter((file) =>
      selectedItems.includes(file.id)
    );
    console.log("选中的文件:", selectedFiles);

    // 下载完成后清空选中状态
    setSelectedItems([]);
  };

  // 处理排序
  const handleSort = (type: number) => {
    if (sortType === type) {
      // 如果点击的是当前排序类型，则切换排序方向
      setSortOrder(sortOrder === 0 ? 1 : 0);
    } else {
      // 否则切换排序类型，默认升序
      setSortType(type);
      setSortOrder(0);
    }
  };

  // 获取排序图标
  const getSortIcon = (type: number) => {
    if (sortType !== type) {
      return (
        <span className={styles.sortIconContainer}>
          <CaretUpOutlined className={styles.sortIcon} />
          <CaretDownOutlined className={styles.sortIcon} />
        </span>
      );
    }

    return (
      <span className={styles.sortIconContainer}>
        <CaretUpOutlined
          className={`${styles.sortIcon} ${
            sortOrder === 0 ? styles.activeIcon : ""
          }`}
        />
        <CaretDownOutlined
          className={`${styles.sortIcon} ${
            sortOrder === 1 ? styles.activeIcon : ""
          }`}
        />
      </span>
    );
  };

  // 渲染文件列表头部
  const renderFileListHeader = () => {
    return (
      <div className={styles.fileListHeader}>
        <div className={styles.fileNameHeader} onClick={() => handleSort(0)}>
          文件名 {getSortIcon(0)}
        </div>
        <div className={styles.fileTimeHeader} onClick={() => handleSort(2)}>
          修改时间 {getSortIcon(2)}
        </div>
        <div className={styles.fileSizeHeader} onClick={() => handleSort(1)}>
          大小 {getSortIcon(1)}
        </div>
        <div className={styles.selectAllHeader}>
          <img
            src={isDarkMode ? outlineDark : outline}
            alt="全选"
            className={`${styles.selectAllIcon} ${fileData.length === 0 ? styles.disabled : ''}`}
            onClick={() => {
              // 如果没有数据，全选按钮不执行任何操作
              if (fileData.length === 0) {
                return;
              }

              const allSelected = selectedItems.length === fileData.length;
              if (allSelected) {
                setSelectedItems([]);
              } else {
                setSelectedItems(fileData.map(item => item.id));
              }
            }}
          />
        </div>
      </div>
    );
  };



  // 渲染空状态
  const renderEmptyState = () => {
    return <EmptyState title="" className={styles.emptyStateWrapper} />;
  };

  // 格式化文件大小
  const formatFileSize = (size: number): string => {
    if (size < 1024) {
      return `${size} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(1)} KB`;
    } else if (size < 1024 * 1024 * 1024) {
      return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    } else {
      return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.actionBar}>
        <div className={styles.title}></div>
        <div className={styles.buttonGroup}>
          <Button onClick={handleUpload} className={styles.uploadButton}>
            上传至网盘
          </Button>
          <Button
            onClick={handleDownloadSelected}
            disabled={selectedItems.length === 0}
            className={styles.downloadButton}
          >
            下载至存储
          </Button>
        </div>
      </div>

      <div className={styles.header}>
        {/* 新的面包屑导航 */}
        <div className={styles.breadcrumbContainer}>
          {breadcrumbs.length > 3 ? (
            <div className={styles.breadcrumbContent}>
              <div className={styles.breadcrumbItem}>...</div>
              {breadcrumbs.slice(-2).map((item, index) => (
                <div
                  className={styles.breadcrumbContent}
                  key={`${item.path}_${index}`}
                >
                  <div className={styles.breadcrumbNextContainer}>
                    <PreloadImage
                      className={styles.breadcrumbNextImg}
                      src={isDarkMode ? next_dark : next}
                      alt="arrow"
                    />
                  </div>
                  <div
                    onClick={() =>
                      handleBreadcrumbClick(
                        item,
                        breadcrumbs.length - 2 + index
                      )
                    }
                    className={`${styles.breadcrumbItem} ${
                      index === 1 ? styles.breadcrumbItemActive : ""
                    }`}
                  >
                    {item.label}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            breadcrumbs.map((item, index) => (
              <div
                className={styles.breadcrumbContent}
                key={`${item.path}_${index}`}
              >
                {index !== 0 && (
                  <div className={styles.breadcrumbNextContainer}>
                    <PreloadImage
                      className={styles.breadcrumbNextImg}
                      src={isDarkMode ? next_dark : next}
                      alt="arrow"
                    />
                  </div>
                )}
                <div
                  onClick={() => handleBreadcrumbClick(item, index)}
                  className={`${styles.breadcrumbItem} ${
                    index === breadcrumbs.length - 1
                      ? styles.breadcrumbItemActive
                      : ""
                  }`}
                >
                  {item.label}
                </div>
              </div>
            ))
          )}
        </div>

        <div className={styles.selectionInfo}>
          {selectedItems.length > 0 && (
            <span>已选中 {selectedItems.length} 项文件/文件夹</span>
          )}
        </div>
      </div>
      <div className={styles.content}>
          <div className={styles.fileListContainer}>
            {renderFileListHeader()}
            {fileData.length > 0 ? (
              <div className={styles.fileListContent}>
                {fileData.map((item) => (
                  <div
                    key={item.id}
                    className={`${styles.fileItem} ${
                      selectedItems.includes(item.id) ? styles.selectedItem : ""
                    }`}
                    onMouseEnter={() => setHoveredItem(item.id)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <div
                      className={styles.fileName}
                      onClick={() => handleItemClick(item)}
                    >
                      <div className={styles.fileIcon}>
                        <PreloadImage src={getFileIcon(item)} alt={item.type} />
                      </div>
                      <div className={styles.fileNameText}>{item.name}</div>
                    </div>
                    <div className={styles.fileTime}>{item.time}</div>
                    <div className={styles.fileSize}>
                      {item.type === "folder" ? "-" : formatFileSize(item.size || 0)}
                    </div>
                    <div className={styles.checkboxCell}>
                      <Checkbox
                        checked={selectedItems.includes(item.id)}
                        onChange={(e) =>
                          handleSelectionChange(item.id, e.target.checked)
                        }
                        onClick={(e) => e.stopPropagation()}
                        className={`${styles.fileCheckbox} ${
                          hoveredItem === item.id || selectedItems.includes(item.id)
                            ? styles.visible
                            : styles.hidden
                        }`}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              !fetchFileListLoading && renderEmptyState()
            )}
          </div>
      </div>

      {/* 上传文件Modal */}
      <UploadModal
        visible={uploadModalVisible}
        onClose={() => setUploadModalVisible(false)}
        onUpload={handleUploadComplete}
      />

      {/* 下载文件Modal */}
      <DownloadModal
        visible={downloadModalVisible}
        onClose={() => setDownloadModalVisible(false)}
        onDownload={handleDownloadComplete}
        selectedFiles={fileData
          .filter((file) => selectedItems.includes(file.id))
          .map((file) => ({
            id: String(file.id),
            name: file.name,
            type: file.type,
            path:
              currentPath === "/"
                ? `/${file.name}`
                : `${currentPath}/${file.name}`,
          }))}
        currentPath={currentPath}
      />
    </div>
  );
};

export default BDfiles;
