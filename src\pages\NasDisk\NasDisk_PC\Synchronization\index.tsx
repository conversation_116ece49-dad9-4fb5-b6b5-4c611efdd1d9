import { FC, useState, useEffect, useCallback } from "react";
import { Progress, message } from "antd";
import { useRequest } from "ahooks";
import start from "@/Resources/icon/start.png";
import pause from "@/Resources/icon/startIcon.png";
import delIcon from "@/Resources/icon/delete.png";
import delDarkIcon from "@/Resources/icon/delete_white.png";
import GuidePanel from "../components/GuidePanel";
import UploadModalTwo from "../components/UploadModalTwo";
import img from '@/Resources/icon/download-automatic.png';
import file_icon from '@/Resources/icon/file-icon.png'
import { PreloadImage } from "@/components/Image";
import { controlTask, ControlTaskParams } from "@/api/nasDisk";
import { modalShow } from "@/components/List";
import request from "@/request";
import styles from './index.module.scss';
import { useUser } from "@/utils/UserContext";
import { useHistory } from "react-router-dom";
import { useTheme } from "@/utils/themeDetector";
import { Toast } from "@/components/Toast/manager";

// 定义同步任务的接口
interface SyncTask {
  id: string;
  name: string;
  status: 'waiting' | 'running' | 'paused' | 'success' | 'failed' | 'success_waiting';
  progress: number;
  localPath: string;
  remotePath: string;
  fileCount: number;
  syncedCount: number;
  createTime: string;
  lastSyncTime?: string;
  errorMessage?: string;
  handledSize: string;
  totalSize: string;
  originalTask?: any; // 保存原始任务对象用于计算更新时间
}

const Synchronization: FC = () => {
  const [tasks, setTasks] = useState<SyncTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [, setHasError] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const { userInfo } = useUser();
  const { nas_vip } = userInfo || {};
  const { isDarkMode } = useTheme();
  const history = useHistory();
  // 字节大小转换函数
  const formatBytes = (bytes: string | number): string => {
    const size = typeof bytes === 'string' ? parseInt(bytes) : bytes;
    if (size === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(size) / Math.log(k));

    return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };


  // 获取任务更新时间
  const getTaskUpdateTime = (task: any) => {
    const timestamp = task.create_time;
    if (!timestamp) return "";

    const taskTime = parseInt(timestamp);
    const now = Date.now();
    const diffMinutes = Math.floor((now - taskTime) / (60 * 1000));

    if (diffMinutes < 1) return "刚刚更新";
    if (diffMinutes < 60) return `${diffMinutes}分钟前更新`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}小时前更新`;

    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 30) return `${diffDays}天前更新`;

    // 格式化日期
    const date = new Date(taskTime);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // 使用useRequest处理任务控制
  const { run: runControlTask } = useRequest(
    (params: ControlTaskParams) => controlTask(params),
    {
      manual: true,
      onSuccess: (result, params) => {
        if (result.code === 0) {
          const commandText: Record<string, string> = {
            pause: "暂停",
            continue: "启动",
            cancel: "取消",
            restart: "重启",
          };
          const command = params[0].command;
          Toast.show(`任务已${commandText[command] || "操作成功"}`);
          // 重新获取任务列表
          fetchTasks();
        } else {
          Toast.show(result.result || "操作失败");
        }
      },
      onError: (error) => {
        console.error("任务操作失败:", error);
        Toast.show("操作失败，请重试");
      },
    }
  );



  // 获取任务数据
  const fetchTasks = useCallback(async () => {
    // 只有在有任务时才显示加载状态，避免空状态时的闪动
    if (tasks.length > 0) {
      setLoading(true);
    }

    try {
      // 获取所有自动上传任务
      const response = await request.post("/taskcenter/get_taskinfo", {
        selector: [
          {
            key: "module",
            value: ["bpan"],
          },
          {
            key: "type",
            value: ["active"],
          },
          {
            key: "action",
            value: ["auto_upload"],
          },
        ],
      }, { showLoading: false }); // 禁用loading动画

      // 处理任务
      if (response?.data?.info) {
        const tasks = response.data.info;
        // 按状态排序：running > paused > waiting > success > 其他
        const sortedTasks = tasks.sort((a: any, b: any) => {
          const statusOrder: Record<string, number> = {
            running: 0,
            paused: 1,
            waiting: 2,
            success: 3,
            failed: 4,
            cancelled: 5,
          };
          return (statusOrder[a.status] || 99) - (statusOrder[b.status] || 99);
        });

        // 转换为SyncTask格式
        const syncTasks: SyncTask[] = sortedTasks.map((task: any) => {
          // 计算显示名称
          let displayName = `任务${task.task_id}`;
          if (task.src && task.src.length > 0) {
            const path = task.src[task.src.length - 1];
            const pathParts = path.split('/').filter((part: string) => part.length > 0);
            displayName = pathParts.length > 0 ? pathParts[pathParts.length - 1] : path;
          }

          return {
            id: task.task_id,
            name: displayName,
            status: mapTaskStatus(task.status),
            progress: task.detail?.progress || 0,
            localPath: task.src?.join("/") || "",
            remotePath: task.dst || "",
            fileCount: task.detail?.total_file_cnt || 0,
            syncedCount: task.detail?.finish_file_cnt || 0,
            createTime: formatTime(task.create_time),
            lastSyncTime: formatTime(task.done_time),
            handledSize: task.detail?.handle_size || "0",
            totalSize: task.detail?.total_size || "0",
            // 保存原始任务对象用于计算更新时间
            originalTask: task,
          };
        });

        setTasks(syncTasks);
      } else {
        setTasks([]);
      }

      setHasError(false);
    } catch (error) {
      console.error("获取同步任务失败:", error);
      setHasError(true);
    } finally {
      setLoading(false);
      setIsInitialized(true);
    }
  }, [tasks.length]);

  // 映射任务状态
  const mapTaskStatus = (status: string): SyncTask['status'] => {
    switch (status) {
      case "running":
        return "running";
      case "success":
        return "success";
      case "failed":
        return "failed";
      case "paused":
        return "paused";
      case "success_waiting":
        return "success_waiting";
      case "waiting":
        return "waiting";
      default:
        return "waiting";
    }
  };


  // 格式化时间 - 处理毫秒时间戳
  const formatTime = (timestamp: string): string => {
    if (!timestamp) return "";
    const date = new Date(parseInt(timestamp));
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };



  // 初始化数据
  useEffect(() => {
    // 立即获取数据
    fetchTasks();
  }, [fetchTasks]);

  // 根据任务数量设置轮询
  useEffect(() => {
    // 只有当有任务时才设置轮询
    let timer: NodeJS.Timeout | null = null;

    if (tasks.length > 0) {
      timer = setInterval(fetchTasks, 5000); // 每5秒轮询任务
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [tasks.length, fetchTasks]);

  // 处理新增同步任务
  const handleAddTask = () => {
    if (tasks.length >= 5) {
      message.warning('最多选择五个文件夹');
      return;
    }
    setUploadModalVisible(true);
  };

  // 处理开通会员按钮点击
  const handleMemberUpgrade = () => {
    // 跳转到会员引导页面
    history.push(`/baiduNetdisk_pc/mine`);
  };

  // 处理弹窗关闭
  const handleModalClose = () => {
    setUploadModalVisible(false);
  };

  // 处理选择文件夹上传
  const handleFolderUpload = (selectedPaths: string[]) => {
    console.log("选择的文件夹路径:", selectedPaths);
    // 这里可以添加具体的上传逻辑
    Toast.show(`成功选择了 ${selectedPaths.length} 个文件夹`);
    setUploadModalVisible(false);
    // 重新获取任务列表
    fetchTasks();
  };

  // 处理任务操作
  const handleTaskAction = (taskId: string, action: 'pause' | 'resume' | 'delete') => {
    if (action === 'delete') {
      handleDeleteTask(taskId);
      return;
    }

    try {
      // 根据动作映射到API命令
      const commandMap: Record<string, 'pause' | 'continue'> = {
        pause: 'pause',
        resume: 'continue',
      };

      const command = commandMap[action];
      if (command) {
        runControlTask({
          task_id: [taskId],
          command: command,
        });
      }
    } catch (error) {
      console.error("操作任务失败:", error);
      Toast.show("操作失败，请重试");
    }
  };

  // 删除任务
  const handleDeleteTask = (taskId: string) => {
    modalShow(
      "删除自动上传任务",
      <div className={styles.deleteConfirmContent}>
        仅删除自动上传任务，不会删除已上传文件
      </div>,
      (modal) => {
        // 确认删除
        runControlTask({
          task_id: [taskId],
          command: "cancel",
        });
        modal.destroy();
      },
      () => {
        // 取消删除
        console.log("取消删除任务");
      },
      false,
      {
        position: "center",
        okBtnText: "确定",
        cancelBtnText: "取消",
        contentHeight: "auto",
      }
    );
  };


  // 如果还没有初始化完成，显示加载状态
  if (!isInitialized) {
    return <div className={styles.loading}></div>;
  }

  // 如果没有任务，显示引导界面
  if (tasks.length === 0 && !loading) {
    return (
      <div className={styles.synchronizationContainer}>
        <GuidePanel
          imageSrc={img}
          imageAlt="智能存储自动上传至百度网盘"
          title="智能存储自动上传至百度网盘"
          description="智能存储的文件夹设置为自动上传后，智能存储会定期（非休眠期间每小时）检查此文件夹是否有新文件加入，如果有新文件，将会自动把新增文件上传到百度网盘。"
          buttonText={nas_vip ? "新增自动上传任务" : "开通会员，享受自动上传特权"}
          onButtonClick={nas_vip ? handleAddTask : handleMemberUpgrade}
        />


        {/* 上传文件夹选择弹窗 */}
        <UploadModalTwo
          visible={uploadModalVisible}
          onClose={handleModalClose}
          onUpload={handleFolderUpload}
        />
      </div>
    );
  }

  // 有任务时显示任务列表
  return (
    <div className={styles.synchronizationContainer}>
      <div className={styles.header}>
        <div className={styles.title}>
          <h2>自动上传的文件夹</h2>
          <span className={styles.taskCount}>({tasks.length}/5)</span>
        </div>
        <span
          onClick={handleAddTask}
          className={`${styles.addButton} ${tasks.length >= 5 ? styles.disabled : styles.enabled}`}
        >
          新增
        </span>
      </div>

      <div className={styles.taskList}>
        {tasks.map(task => (
          <div key={task.id} className={styles.taskItem}>
            <div className={styles.taskRow}>
              <div className={styles.taskLeft}>
                <div className={styles.folderIcon}>
                  <PreloadImage style={{ width: '40px', height: '40px' }} src={file_icon} alt="文件夹图标" />
                </div>
                <div className={styles.taskInfo}>
                  <div className={styles.taskName}>{task.name}</div>

                </div>
              </div>

              <div className={styles.taskCenter}>

                <div className={styles.progressContainer}>
                  <div className={styles.progressInfo}>
                    <span className={styles.statusText}>
                      {task.status === 'waiting' ? `等待中/${formatBytes(task.totalSize)}` :
                        task.status === 'running' ? `${formatBytes(task.handledSize)}/${formatBytes(task.totalSize)}` :
                          task.status === 'success' ? '已完成' :
                            task.status === 'paused' ? '已暂停' :
                              task.status === 'failed' ? '同步失败' :
                                task.status === 'success_waiting' ? '已完成' :
                                  `${formatBytes(task.handledSize)}/${formatBytes(task.totalSize)}`}
                    </span>
                    <div className={styles.fileCount}>
                      {task.syncedCount}/{task.fileCount}
                    </div>
                  </div>

                  <Progress
                    percent={task.progress}
                    showInfo={false}
                    strokeColor="var(--primary-color)"
                    className={`${styles.progressBar} ${styles[task.status]}`}
                  />
                  <div className={styles.taskPathContainer}>
                    <div className={styles.taskPath}>
                      {task.remotePath}
                    </div>
                    <div className={styles.updateTime}>
                      {task.originalTask ? getTaskUpdateTime(task.originalTask) : ''}
                    </div>
                  </div>
                </div>
              </div>

              <div className={styles.taskRight}>

                <div className={styles.actionButtons}>
                  {(task.status === 'waiting' || task.status === 'running') ? (
                    <img
                      src={start}
                      alt="开始"
                      className={styles.controlIcon}
                      onClick={() => handleTaskAction(task.id, 'pause')}
                    />

                  ) : task.status === 'paused' ? (
                    <img
                      src={pause}
                      alt="暂停"
                      className={styles.controlIcon}
                      onClick={() => handleTaskAction(task.id, 'resume')}
                    />

                  ) : task.status === 'success' ? null : (
                    <img
                      src={start}
                      alt="暂停"
                      className={styles.controlIcon}
                      onClick={() => handleTaskAction(task.id, 'resume')}
                    />

                  )}
                  <img
                    src={isDarkMode ? delDarkIcon : delIcon}
                    alt="删除"
                    className={styles.deleteImg}
                    style={{width:'25px',height:'25px'}}
                    onClick={() => handleTaskAction(task.id, 'delete')}
                  />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>



      {/* 上传文件夹选择弹窗 */}
      <UploadModalTwo
        visible={uploadModalVisible}
        onClose={handleModalClose}
        onUpload={handleFolderUpload}
      />
    </div>
  );
};

export default Synchronization;