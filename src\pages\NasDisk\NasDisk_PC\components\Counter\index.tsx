import React, { useState, useEffect } from 'react';
import { message } from 'antd';
import styles from './index.module.scss';
import { Toast } from '@/components/Toast/manager';

interface CounterProps {
  /** 是否显示收银台 */
  isVisible: boolean;
  /** 关闭收银台的回调函数 */
  onClose: () => void;
  /** 支付成功的回调函数 */
  onPaymentSuccess?: () => void;
  /** 支付失败的回调函数 */
  onPaymentError?: (error: string) => void;
  /** 支付取消的回调函数 */
  onPaymentCancel?: () => void;
  /** NAS ID，默认为 '123' */
  nasId?: string;
  /** Access Token，默认为 '456' */
  accessToken?: string;
  /** 是否显示头部，默认为 true */
  showHeader?: boolean;
  /** 自定义标题 */
  title?: string;
}

const Counter: React.FC<CounterProps> = ({
  isVisible,
  onClose,
  onPaymentSuccess,
  onPaymentError,
  onPaymentCancel,
  nasId = '123',
  accessToken = '456',
  showHeader = true,
  title = '开通百度网盘NAS会员'
}) => {
  const [cashierUrl, setCashierUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // 初始化收银台URL
  useEffect(() => {
    if (isVisible) {
      // 使用用户提供的URL格式
      const paymentUrl = `https://pan.baidu.com/ent/nas/cashier?nas_id=${nasId}&access_token=${encodeURIComponent(accessToken)}`;
      setCashierUrl(paymentUrl);
      setIsLoading(true);
      
      message.info('正在加载支付页面...');
    } else {
      setCashierUrl('');
      setIsLoading(false);
    }
  }, [isVisible, nasId, accessToken]);

  // 监听支付页面的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 检查消息来源是否为百度网盘域名
      if (event.origin !== 'https://pan.baidu.com') {
        return;
      }

      // 处理支付相关消息
      if (event.data && typeof event.data === 'object') {
        const { type, status } = event.data;
        
        if (type === 'payment') {
          if (status === 'success') {
            // 支付成功
            Toast.show('支付成功！会员已开通');
            onPaymentSuccess?.();
            onClose();
          } else if (status === 'cancel') {
            // 用户取消支付
            message.warning('支付已取消');
            onPaymentCancel?.();
            onClose();
          } else if (status === 'error') {
            // 支付失败
            const errorMsg = '支付失败，请重试';
            Toast.show(errorMsg);
            onPaymentError?.(errorMsg);
          }
        }
      }
    };

    if (isVisible) {
      // 添加消息监听器
      window.addEventListener('message', handleMessage);
    }

    // 清理函数
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [isVisible, onClose, onPaymentSuccess, onPaymentError, onPaymentCancel]);

  // 处理支付页面加载完成
  const handlePaymentLoad = () => {
    setIsLoading(false);
    Toast.show('支付页面加载完成');
  };

  // 处理支付页面加载错误
  const handlePaymentError = () => {
    setIsLoading(false);
    Toast.show('支付页面加载失败，请重试');
  };

  // 处理关闭
  const handleClose = () => {
    onClose();
  };

  // 处理遮罩点击
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className={styles.counterOverlay} onClick={handleOverlayClick}>
      <div className={styles.counterModal}>
        {showHeader && (
          <div className={styles.counterHeader}>
            <div className={styles.counterTitle}>{title}</div>
            <div className={styles.counterClose} onClick={handleClose}>
              ✕
            </div>
          </div>
        )}
        
        <div className={styles.counterContainer}>
          {isLoading && (
            <div className={styles.loadingOverlay}>
              <div className={styles.loadingSpinner}></div>
              <div className={styles.loadingText}>正在加载支付页面...</div>
            </div>
          )}
          
          {cashierUrl && (
            <iframe
            title="百度支付收银台" 
              src={cashierUrl}
              className={styles.counterIframe}
              frameBorder="0"
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                display: isLoading ? 'none' : 'block'
              }}
              allow="payment"
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
              onLoad={handlePaymentLoad}
              onError={handlePaymentError}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default Counter; 