import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, List, message } from "antd";
import { ArrowLeftOutlined, RightOutlined } from "@ant-design/icons";
import { useRequest } from "ahooks";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
import folderIcon from "@/Resources/icon/file-icon.png";
import { getBaiduNetdiskFileList, BaiduFileItem } from "@/api/nasDisk";
import CreateBdFolder from "../CreateBdFolder";
import createIcon from "@/Resources/icon/create.png";
import createIconDark from "@/Resources/icon/create-dark.png";
import { useTheme } from "@/utils/themeDetector";
import { Toast } from "@/components/Toast/manager";

interface UploadLocationModalProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (selectedPath: string, displayPath: string) => void;
}

interface FileItem {
  id: string;
  fs_id: number;
  name: string;
  type: "folder" | "file";
  time: string;
  isLiked?: boolean;
  path: string;
  isDirectory?: boolean;
  size?: number;
}

const UploadLocationModal: React.FC<UploadLocationModalProps> = ({
  visible,
  onClose,
  onSelect,
}) => {
  const { isDarkMode } = useTheme();
  // 面包屑导航路径
  const [breadcrumbPath, setBreadcrumbPath] = useState<
    { label: string; path: string }[]
  >([{ label: "百度网盘", path: "/" }]);

  // 当前路径
  const [currentPath, setCurrentPath] = useState<string>("/");

  // 文件列表
  const [fileList, setFileList] = useState<FileItem[]>([]);

  // 选中的文件夹
  const [selectedFolder, setSelectedFolder] = useState<FileItem | null>(null);

  // 新建文件夹相关状态
  const [newFolderModalVisible, setNewFolderModalVisible] =
    useState<boolean>(false);

  // 加载状态
  const [loading, setLoading] = useState<boolean>(false);

  // 获取百度网盘文件列表
  const { run: fetchFileList } = useRequest(
    (params: { path: string }) => {
      setLoading(true);
      return getBaiduNetdiskFileList({
        action: "remotelist",
        path: params.path,
        order: "name",
        desc: 0,
        web: 1,
        folder: 1, // 只返回文件夹
      });
    },
    {
      manual: true,
      onSuccess: (response) => {
        if (response && response.errno === 0) {
          // 将百度网盘文件列表转换为应用内文件列表格式
          const folders: FileItem[] = response.list
            .filter((item: BaiduFileItem) => item.isdir === 1) // 只保留文件夹
            .map((item: BaiduFileItem, index) => ({
              id: `folder_${index}`,
              fs_id: item.fs_id,
              name: item.server_filename,
              type: "folder",
              time: new Date(item.server_mtime * 1000).toLocaleString("zh-CN", {
                year: "numeric",
                month: "2-digit",
                day: "2-digit",
                hour: "2-digit",
                minute: "2-digit",
              }),
              path: item.path,
              isDirectory: true,
              size: item.size,
            }));

          setFileList(folders);
          setLoading(false);
        } else {
          console.error("获取文件列表失败:", response);
          Toast.show("获取文件列表失败，请重试");
          setFileList([]);
          setLoading(false);
        }
      },
      onError: (error) => {
        console.error("获取文件列表失败：", error);
        Toast.show("获取文件列表失败，请重试");
        setFileList([]);
        setLoading(false);
      },
    }
  );

  // 初始化时获取根目录文件列表
  useEffect(() => {
    if (visible) {
      setSelectedFolder(null);
      setBreadcrumbPath([{ label: "百度网盘", path: "/" }]);
      setCurrentPath("/");
      fetchFileList({ path: "/" });
    }
  }, [visible, fetchFileList]);

  // 处理文件夹点击
  const handleFolderClick = (folder: FileItem) => {
    // 导航到子文件夹
    setCurrentPath(folder.path);
    setBreadcrumbPath((prev) => [
      ...prev,
      { label: folder.name, path: folder.path },
    ]);

    // 获取子文件夹内容
    fetchFileList({ path: folder.path });

    // 清空选择
    setSelectedFolder(null);
  };

  // 处理文件夹选择
  const handleFolderSelect = (folder: FileItem) => {
    // 如果点击的是当前选中的文件夹，则取消选择
    if (selectedFolder?.id === folder.id) {
      setSelectedFolder(null);
    } else {
      // 否则选中该文件夹
      setSelectedFolder(folder);
    }
  };

  // 处理面包屑导航点击
  const handleBreadcrumbClick = (
    item: { label: string; path: string },
    index: number
  ) => {
    // 如果点击的是当前位置，不做任何操作
    if (index === breadcrumbPath.length - 1) {
      return;
    }

    // 截取到点击的位置
    const newPath = breadcrumbPath.slice(0, index + 1);
    setBreadcrumbPath(newPath);

    // 获取指定路径的目录列表
    const targetPath = item.path;
    setCurrentPath(targetPath);

    // 获取该路径下的内容
    fetchFileList({ path: targetPath });

    // 更新选中的文件夹
    setSelectedFolder(null);
  };

  // 处理确认选择
  const handleConfirm = () => {
    // 如果当前目录为根目录，直接使用根目录
    if (!selectedFolder && breadcrumbPath.length <= 1) {
      message.warning("请选择一个文件夹");
      return;
    }

    // 使用当前路径或选中的文件夹路径
    const uploadPath = selectedFolder ? selectedFolder.path : currentPath;

    // 获取显示路径
    const displayPath = selectedFolder
      ? breadcrumbPath.map((item) => item.label).join("/") +
        "/" +
        selectedFolder.name
      : breadcrumbPath.map((item) => item.label).join("/");

    // 调用选择回调
    onSelect(uploadPath, displayPath);
    onClose();
  };

  // 打开新建文件夹弹窗
  const showNewFolderModal = () => {
    setNewFolderModalVisible(true);
  };

  // 关闭新建文件夹弹窗
  const closeNewFolderModal = () => {
    setNewFolderModalVisible(false);
  };

  // 创建文件夹成功后的回调
  const handleFolderCreated = () => {
    // 刷新当前目录
    fetchFileList({ path: currentPath });
    closeNewFolderModal();
  };

  // 渲染面包屑导航
  const renderBreadcrumb = () => {
    return (
      <div className={styles.breadcrumbContainer}>
        {breadcrumbPath.map((item, index) => (
          <React.Fragment key={index}>
            {index > 0 && (
              <span className={styles.breadcrumbSeparator}>&gt;</span>
            )}
            <span
              className={`${styles.breadcrumbItem} ${
                index === breadcrumbPath.length - 1
                  ? styles.breadcrumbCurrent
                  : styles.breadcrumbLink
              }`}
              onClick={() => handleBreadcrumbClick(item, index)}
            >
              {item.label}
            </span>
          </React.Fragment>
        ))}
      </div>
    );
  };

  // 渲染新建文件夹按钮
  const renderNewFolderButton = () => {
    return (
      <div className={styles.newFolderButton} onClick={onClose}>
        <div className={styles.newFolderText}>取消</div>
      </div>
    );
  };

  return (
    <Modal
      title={
        <div className={styles.modalHeader}>
          <ArrowLeftOutlined className={styles.backIcon} onClick={onClose} />
          <span className={styles.modalTitle}>选择上传位置</span>
          <PreloadImage
            src={isDarkMode ? createIconDark : createIcon}
            alt="新建文件夹"
            style={{width:40,height:40}}
            onClick={showNewFolderModal}
          />
        </div>
      }
      open={visible}
      footer={null}
      onCancel={onClose}
      width={546}
      className={styles.uploadLocationModal}
      closeIcon={null}
      centered
      styles={{
        body: {
          height: "calc(636px - 95px)",
          padding: 0,
          overflow: "hidden",
        },
      }}
    >
      <div className={styles.modalContent}>
        <div className={styles.breadcrumbHeader}>{renderBreadcrumb()}</div>

        <div className={styles.fileListContainer}>
          {loading ? (
            <div className={styles.emptyContainer}>
              <div className={styles.emptyText}>加载中...</div>
            </div>
          ) : fileList.length > 0 ? (
            <List
              className={styles.fileList}
              dataSource={fileList}
              renderItem={(file) => (
                <List.Item
                  key={file.id}
                  className={`${styles.fileItem} ${
                    selectedFolder?.id === file.id ? styles.selectedItem : ""
                  }`}
                >
                  <div
                    className={`${styles.fileContent} ${
                      selectedFolder?.id === file.id
                        ? styles.selectedContent
                        : ""
                    }`}
                    onClick={() => handleFolderSelect(file)}
                    title="点击选择文件夹"
                  >
                    <PreloadImage
                      src={folderIcon}
                      alt="folder"
                      className={styles.fileIcon}
                    />
                    <div className={styles.fileInfo}>
                      <div className={styles.fileName}>{file.name}</div>
                      <div className={styles.fileDetails}>{file.time}</div>
                    </div>
                  </div>
                  <div
                    className={styles.arrowIcon}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFolderClick(file);
                    }}
                    title="进入文件夹"
                  >
                    <RightOutlined />
                  </div>
                </List.Item>
              )}
            />
          ) : (
            <div className={styles.emptyContainer}>
              <div className={styles.emptyText}>该目录下没有文件夹</div>
            </div>
          )}
        </div>

        <div className={styles.footerContainer}>
          <div className={styles.footerLeft}>{renderNewFolderButton()}</div>
          <div className={styles.footerRight}>
            <Button
              type="primary"
              className={styles.confirmButton}
              disabled={!selectedFolder && breadcrumbPath.length <= 1}
              onClick={handleConfirm}
            >
              确定
            </Button>
          </div>
        </div>
      </div>

      {/* 新建文件夹弹窗 */}
      <CreateBdFolder
        visible={newFolderModalVisible}
        onCancel={closeNewFolderModal}
        onSuccess={handleFolderCreated}
        currentPath={currentPath}
      />
    </Modal>
  );
};

export default UploadLocationModal;
